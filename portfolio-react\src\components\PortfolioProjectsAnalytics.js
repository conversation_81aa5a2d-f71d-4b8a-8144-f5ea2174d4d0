import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { <PERSON>a<PERSON><PERSON><PERSON>, <PERSON>a<PERSON>ouse<PERSON>ointer, Fa<PERSON>lock, FaUsers, FaChartLine, FaExternalLinkAlt, FaExclamationTriangle, FaCheckCircle, FaLink } from 'react-icons/fa';
import './PortfolioProjectsAnalytics.css';
import { preemptiveWakeup } from '../utils/backendWakeup';
import { formatDuration } from '../hooks/useVisitorTracking';
import { API_CONFIG } from '../config/apiConfig';
import { safeFetch, isExtensionError } from '../utils/extensionErrorHandler';
import { findPortfolioProject, isValidPortfolioProject } from '../config/projectsConfig';
import FixedBackButton from './FixedBackButton';

// Portfolio items data (using centralized configuration with image mapping)
const portfolioItems = [
  { // ID : 1
    href: "https://threed-e-commerce.onrender.com",
    image: "/3D E-Comm.PNG",
    alt: "3D Ecommerce",
    title: "3D Ecommerce",
    forSale: true,
    id: 'portfolio-0-3d-ecommerce'
  },
  { // ID : 2
    href: "#",
    image: "/ex1.webp",
    alt: "Will be deployed soon.",
    title: "Will be deployed soon.",
    forSale: false,
    id: 'portfolio-1-will-be-deployed-soon'
  },
  { // ID : 3
    href: "https://creative-website-jumper.onrender.com",
    image: "/P1.PNG",
    alt: "Nexit Brand Identity",
    title: "Professional Portfolio",
    forSale: true,
    id: 'portfolio-2-professional-portfolio'
  },
  { // ID : 4
    href: "#",
    image: "/ex3.webp",
    alt: "Will be deployed soon.s",
    title: "Will be deployed soon.",
    forSale: false,
    id: 'portfolio-3-will-be-deployed-soon'
  },
  { // ID : 5
    href: "https://flawless-2pqq.onrender.com",
    image: "/Flaw.PNG",
    alt: "Flaw",
    title: "Available",
    forSale: true,
    id: 'portfolio-4-available'
  },
  { // ID : 6
    href: "#",
    image: "/ex5.png",
    alt: "Will be deployed soon.",
    title: "Will be deployed soon.",
    forSale: false,
    id: 'portfolio-5-will-be-deployed-soon'
  },
  { // ID : 7
    href: "https://hoobank-neon-future.onrender.com",
    image: "/HooBank.png",
    alt: "Business Web UI",
    title: "Experience digital banking with AI ",
    forSale: true,
    id: 'portfolio-6-experience-digital-banking-with-ai'
  }
];

const PortfolioProjectsAnalytics = () => {
  const [analytics, setAnalytics] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [activeTab, setActiveTab] = useState('all'); // 'all', 'available', 'unavailable'
  const navigate = useNavigate();

  // Helper function to get portfolio item data using centralized project configuration
  const getPortfolioItemData = (projectId, projectTitle) => {
    // Use centralized configuration to find the project
    const portfolioProject = findPortfolioProject(projectId, projectTitle);

    if (portfolioProject) {
      // Find the corresponding portfolio item with image data
      const portfolioItem = portfolioItems.find(item => item.id === portfolioProject.id);

      // Return the project data from configuration with image data
      return {
        title: portfolioProject.title,
        href: portfolioProject.href,
        forSale: portfolioProject.forSale,
        image: portfolioItem?.image || '/default-project.png',
        alt: portfolioItem?.alt || portfolioProject.title
      };
    }

    // Fallback to direct matching if not found in configuration
    if (projectId) {
      const directMatch = portfolioItems.find(item => item.id === projectId);
      if (directMatch) return directMatch;
    }

    if (projectTitle) {
      return portfolioItems.find(item =>
        item.title.toLowerCase().includes(projectTitle.toLowerCase()) ||
        projectTitle.toLowerCase().includes(item.title.toLowerCase())
      );
    }

    return null;
  };

  useEffect(() => {
    fetchAnalytics();
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  const fetchAnalytics = async () => {
    try {
      setLoading(true);
      setError('');

      // Wake up backend before attempting to fetch analytics
      console.log('Waking up backend...');
      await preemptiveWakeup();

      const token = localStorage.getItem('token');
      if (!token) {
        navigate('/admin/login');
        return;
      }

      console.log('Fetching portfolio analytics from:', API_CONFIG.ENDPOINTS.PORTFOLIO_PROJECTS_ANALYTICS);

      const response = await safeFetch(API_CONFIG.ENDPOINTS.PORTFOLIO_PROJECTS_ANALYTICS, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      console.log('Response status:', response.status);

      if (response.status === 401) {
        localStorage.removeItem('token');
        navigate('/admin/login');
        return;
      }

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Response error:', errorText);
        throw new Error(`Failed to fetch analytics (${response.status}): ${errorText}`);
      }

      const data = await response.json();
      console.log('📊 Analytics data received:', data);
      console.log('📊 Raw data structure:', {
        hasData: !!data.data,
        dataLength: data.data?.length || 0,
        hasAvailableProjects: !!data.availableProjects,
        availableLength: data.availableProjects?.length || 0,
        hasUnavailableProjects: !!data.unavailableProjects,
        unavailableLength: data.unavailableProjects?.length || 0
      });

      // Additional frontend filtering using centralized project configuration
      if (data.success && data.data) {
        console.log('Raw data received from backend:', data);

        const dedupeProjects = (projects) => {
          const seen = new Set();
          const duplicatesFound = [];

          const uniqueProjects = projects.filter(project => {
            // Create a unique identifier based on BOTH projectId AND projectTitle
            // This ensures we only remove true duplicates (same ID AND same title)
            const combinedKey = `${project.projectId || 'no-id'}-${project.projectTitle || 'no-title'}`;

            // Only consider it a duplicate if the EXACT combination has been seen
            if (seen.has(combinedKey)) {
              duplicatesFound.push(project);
              console.log('🔍 Duplicate project found and removed:', {
                projectId: project.projectId,
                projectTitle: project.projectTitle,
                combinedKey: combinedKey
              });
              return false;
            }

            seen.add(combinedKey);
            return true;
          });

          if (duplicatesFound.length > 0) {
            console.log(`🚨 Total duplicates removed: ${duplicatesFound.length}`);
            console.log('📋 Duplicate projects details:', duplicatesFound);
          } else {
            console.log('✅ No duplicates found in this dataset');
          }

          return uniqueProjects;
        };

        const filterProjects = (projects) => {
          console.log('🔍 Projects before filtering:', projects.length);
          console.log('🔍 Raw projects data:', projects.map(p => ({
            projectId: p.projectId,
            projectTitle: p.projectTitle,
            availabilityStatus: p.availabilityStatus
          })));

          const validProjects = projects.filter(project => {
            const isValid = isValidPortfolioProject(project.projectId, project.projectTitle);
            if (!isValid) {
              console.log('❌ Invalid project filtered out:', {
                projectId: project.projectId,
                projectTitle: project.projectTitle,
                reason: 'Failed isValidPortfolioProject check'
              });
            } else {
              console.log('✅ Valid project kept:', {
                projectId: project.projectId,
                projectTitle: project.projectTitle
              });
            }
            return isValid;
          });
          console.log('🔍 Projects after validation filtering:', validProjects.length);

          const dedupedProjects = dedupeProjects(validProjects);
          console.log('🔍 Projects after deduplication:', dedupedProjects.length);

          const finalProjects = dedupedProjects.slice(0, 7); // Ensure maximum of 7 projects
          console.log('🔍 Final projects after limit:', finalProjects.length);

          return finalProjects;
        };

        const filteredAllProjects = filterProjects(data.data);
        const filteredAvailableProjects = data.availableProjects ? filterProjects(data.availableProjects) : [];
        const filteredUnavailableProjects = data.unavailableProjects ? filterProjects(data.unavailableProjects) : [];

        // Check for data consistency issues
        console.log('🔍 Checking data consistency:');
        console.log('Main data array length:', filteredAllProjects.length);
        console.log('Available projects length:', filteredAvailableProjects.length);
        console.log('Unavailable projects length:', filteredUnavailableProjects.length);
        console.log('Expected total:', filteredAvailableProjects.length + filteredUnavailableProjects.length);

        // If the main data array is missing projects, reconstruct it from available + unavailable
        let finalAllProjects = filteredAllProjects;
        if (filteredAllProjects.length < (filteredAvailableProjects.length + filteredUnavailableProjects.length)) {
          console.log('🔧 Main data array is incomplete, reconstructing from available + unavailable arrays');
          finalAllProjects = [...filteredAvailableProjects, ...filteredUnavailableProjects];
          console.log('Reconstructed main data array length:', finalAllProjects.length);
        }

        // Recalculate summary counts based on filtered data
        const recalculatedSummary = {
          ...data.summary,
          availableProjectsCount: filteredAvailableProjects.length,
          unavailableProjectsCount: filteredUnavailableProjects.length
        };

        const filteredData = {
          ...data,
          data: finalAllProjects,
          availableProjects: filteredAvailableProjects,
          unavailableProjects: filteredUnavailableProjects,
          totalProjects: finalAllProjects.length,
          summary: recalculatedSummary
        };

        console.log('🔍 Data count comparison:');
        console.log('Backend counts:', {
          total: data.totalProjects,
          available: data.summary?.availableProjectsCount,
          unavailable: data.summary?.unavailableProjectsCount
        });
        console.log('Frontend filtered counts:', {
          total: filteredData.totalProjects,
          available: filteredData.summary.availableProjectsCount,
          unavailable: filteredData.summary.unavailableProjectsCount
        });

        // Detailed breakdown of what's in each dataset
        console.log('📊 Detailed data breakdown:');
        console.log('All projects (filtered):', filteredData.data.map(p => ({ id: p.projectId, title: p.projectTitle })));
        console.log('Available projects (filtered):', filteredData.availableProjects.map(p => ({ id: p.projectId, title: p.projectTitle })));
        console.log('Unavailable projects (filtered):', filteredData.unavailableProjects.map(p => ({ id: p.projectId, title: p.projectTitle })));

        console.log('Final filtered data:', filteredData);

        setAnalytics(filteredData);
      } else {
        setAnalytics(data);
      }
    } catch (err) {
      // Don't show errors for extension-related issues
      if (!isExtensionError(err)) {
        console.error('Analytics fetch error:', err);
        setError(`Error loading analytics: ${err.message}`);
      } else {
        console.log('Extension error suppressed in PortfolioProjectsAnalytics:', err.message);
      }
    } finally {
      setLoading(false);
    }
  };

  const handleProjectClick = (projectUrl) => {
    if (projectUrl && projectUrl !== '' && projectUrl !== '#') {
      window.open(projectUrl, '_blank');
    }
  };

  const handleBackClick = () => {
    navigate('/admin/dashboard');
  };

  const getDisplayData = () => {
    if (!analytics) return [];

    let displayData;
    switch (activeTab) {
      case 'available':
        displayData = analytics.availableProjects || [];
        break;
      case 'unavailable':
        displayData = analytics.unavailableProjects || [];
        break;
      default:
        displayData = analytics.data || [];
    }

    console.log(`📋 Display data for tab '${activeTab}':`, {
      length: displayData.length,
      projects: displayData.map(p => ({
        projectId: p.projectId,
        projectTitle: p.projectTitle,
        availabilityStatus: p.availabilityStatus
      }))
    });

    return displayData;
  };

  if (loading) {
    return (
      <div className="portfolio-analytics-container">
        <div className="loading-spinner">
          <div className="spinner"></div>
          <p>Loading Portfolio Analytics...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="portfolio-analytics-container">
        <div className="error-message">
          <h3>Error Loading Analytics</h3>
          <p>{error}</p>
          <button onClick={fetchAnalytics} className="retry-button">
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="portfolio-analytics-container">
      {/* Fixed Back Button */}
      <FixedBackButton onClick={handleBackClick} text="Back to Dashboard" />

      <div className="analytics-header">
        <h1><FaFolder /> Portfolio Projects Analytics</h1>
        <p>Detailed analytics for portfolio carousel project interactions</p>
      </div>

      {analytics && (
        <>
          {/* Summary Cards */}
          <div className="analytics-summary">
            <div className="summary-card">
              <FaFolder className="summary-icon" />
              <div className="summary-content">
                <h3>Total Projects</h3>
                <div className="summary-value">{analytics.totalProjects}</div>
              </div>
            </div>
            <div className="summary-card">
              <FaMousePointer className="summary-icon" />
              <div className="summary-content">
                <h3>Total Interactions</h3>
                <div className="summary-value">{analytics.summary.totalInteractions}</div>
              </div>
            </div>
            <div className="summary-card">
              <FaClock className="summary-icon" />
              <div className="summary-content">
                <h3>Total Time</h3>
                <div className="summary-value">{formatDuration(analytics.summary.totalDuration)}</div>
              </div>
            </div>
            <div className="summary-card">
              <FaChartLine className="summary-icon" />
              <div className="summary-content">
                <h3>Avg Interactions/Project</h3>
                <div className="summary-value">{analytics.summary.avgInteractionsPerProject}</div>
              </div>
            </div>
          </div>

          {/* Availability Summary */}
          <div className="availability-summary">
            <div className="availability-card available">
              <FaCheckCircle className="availability-icon" />
              <div className="availability-content">
                <h3>Available Projects</h3>
                <div className="availability-value">{analytics.summary.availableProjectsCount}</div>
              </div>
            </div>
            <div className="availability-card unavailable">
              <FaExclamationTriangle className="availability-icon" />
              <div className="availability-content">
                <h3>Unavailable Projects</h3>
                <div className="availability-value">{analytics.summary.unavailableProjectsCount}</div>
              </div>
            </div>
          </div>

          {/* Tab Navigation */}
          <div className="tab-navigation">
            <button 
              className={`tab-button ${activeTab === 'all' ? 'active' : ''}`}
              onClick={() => setActiveTab('all')}
            >
              All Projects ({analytics.totalProjects})
            </button>
            <button 
              className={`tab-button ${activeTab === 'available' ? 'active' : ''}`}
              onClick={() => setActiveTab('available')}
            >
              Available ({analytics.summary.availableProjectsCount})
            </button>
            <button 
              className={`tab-button ${activeTab === 'unavailable' ? 'active' : ''}`}
              onClick={() => setActiveTab('unavailable')}
            >
              Unavailable ({analytics.summary.unavailableProjectsCount})
            </button>
          </div>

          {/* Projects List */}
          <div className="projects-analytics-list">
            {(() => {
              const displayData = getDisplayData();
              return displayData.length > 0 ? (
                <div className="projects-grid">
                  {displayData.map((project, index) => {
                    const portfolioData = getPortfolioItemData(project.projectId, project.projectTitle);

                    // Create a more robust unique key
                    const uniqueKey = `project-${project.projectId || 'no-id'}-${project.projectTitle?.replace(/[^a-zA-Z0-9]/g, '-') || 'no-title'}-${index}`;

                    return (
                      <div key={uniqueKey} className={`project-analytics-card ${project.availabilityStatus === false ? 'unavailable' : 'available'}`}>
                      <div className="project-visual-header">
                        {portfolioData && portfolioData.image && (
                          <div className="project-image-container">
                            <img
                              src={portfolioData.image}
                              alt={portfolioData.alt || portfolioData.title}
                              className="project-image-analytics"
                              onError={(e) => {
                                e.target.style.display = 'none';
                              }}
                            />
                            {portfolioData.forSale && (
                              <div className="for-sale-badge">
                                <span>Available</span>
                              </div>
                            )}
                          </div>
                        )}

                        <div className="project-header">
                          <h3 className="project-title">{project.projectTitle}</h3>

                          <div className="project-meta">
                            <div className="project-status">
                              {project.availabilityStatus !== false ? (
                                <span className="status-badge available">
                                  <FaCheckCircle /> Available
                                </span>
                              ) : (
                                <span className="status-badge unavailable">
                                  <FaExclamationTriangle /> Unavailable
                                </span>
                              )}
                            </div>

                            {portfolioData && portfolioData.href && portfolioData.href !== '#' && (
                              <div className="project-link-info">
                                <FaLink className="link-icon" />
                                <span className="link-text">{portfolioData.href}</span>
                              </div>
                            )}
                          </div>

                          {project.projectUrl && project.projectUrl !== '' && project.projectUrl !== '#' && (
                            <div className="project-actions">
                              <button
                                className="view-project-btn"
                                onClick={() => handleProjectClick(project.projectUrl)}
                              >
                                <FaExternalLinkAlt /> View Project
                              </button>
                            </div>
                          )}
                        </div>
                      </div>

                      <div className="project-stats">
                        <div className="stats-grid">
                          <div className="stat-item primary">
                            <FaMousePointer className="stat-icon" />
                            <div className="stat-content">
                              <span className="stat-value">{project.totalInteractions}</span>
                              <span className="stat-label">Total Interactions</span>
                            </div>
                          </div>
                          <div className="stat-item primary">
                            <FaUsers className="stat-icon" />
                            <div className="stat-content">
                              <span className="stat-value">{project.uniqueVisitorCount}</span>
                              <span className="stat-label">Unique Visitors</span>
                            </div>
                          </div>
                          <div className="stat-item secondary">
                            <FaClock className="stat-icon" />
                            <div className="stat-content">
                              <span className="stat-value">{formatDuration(project.totalDuration)}</span>
                              <span className="stat-label">Total Duration</span>
                            </div>
                          </div>
                          <div className="stat-item secondary">
                            <FaClock className="stat-icon" />
                            <div className="stat-content">
                              <span className="stat-value">{formatDuration(project.avgDuration)}</span>
                              <span className="stat-label">Avg Duration</span>
                            </div>
                          </div>
                        </div>
                      </div>

                      <div className="project-details">
                        <div className="detail-item">
                          <strong>Project ID:</strong>
                          <span className="detail-value">{project.projectId || 'N/A'}</span>
                        </div>
                        <div className="detail-item">
                          <strong>Project Title:</strong>
                          <span className="detail-value">{project.projectTitle || 'N/A'}</span>
                        </div>
                        <div className="detail-item">
                          <strong>Last Interaction:</strong>
                          <span className="detail-value">
                            {project.lastInteraction ?
                              new Date(project.lastInteraction).toLocaleString() :
                              'Never'
                            }
                          </span>
                        </div>
                        {portfolioData && portfolioData.alt && (
                          <div className="detail-item">
                            <strong>Description:</strong>
                            <span className="detail-value">{portfolioData.alt}</span>
                          </div>
                        )}
                      </div>

                      {project.interactionTypes && project.interactionTypes.length > 0 && (
                        <div className="interaction-types">
                          <h4>Interaction Types</h4>
                          <div className="types-list">
                            {project.interactionTypes.map((type, idx) => (
                              <span key={idx} className="interaction-type-badge">{type}</span>
                            ))}
                          </div>
                        </div>
                      )}

                      {project.recentInteractions && project.recentInteractions.length > 0 && (
                        <div className="recent-interactions">
                          <h4>Recent Interactions</h4>
                          <div className="interactions-list">
                            {project.recentInteractions.slice(0, 5).map((interaction, idx) => (
                              <div key={idx} className="interaction-item">
                                <div className="interaction-info">
                                  <span className="interaction-ip">{interaction.ip}</span>
                                  <span className="interaction-type">{interaction.interactionType || 'view'}</span>
                                </div>
                                <div className="interaction-meta">
                                  <span className="interaction-time">
                                    {new Date(interaction.timestamp).toLocaleString()}
                                  </span>
                                  <span className="interaction-duration">
                                    {formatDuration(interaction.duration || 0)}
                                  </span>
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  );
                  })}
                </div>
              ) : (
                <div className="no-data">
                  <p>No portfolio project analytics data available for this category.</p>
                  <p>Data will appear once users start interacting with portfolio projects.</p>
                </div>
              );
            })()}
          </div>
        </>
      )}
    </div>
  );
};

export default PortfolioProjectsAnalytics;
